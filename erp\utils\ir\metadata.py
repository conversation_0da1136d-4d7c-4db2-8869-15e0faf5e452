"""
IR metadata operations
Contains low-level operations for managing IR model and field metadata
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from ...logging import get_logger

logger = get_logger(__name__)


class IRMetadataOperations:
    """
    Low-level operations for IR metadata management
    Handles direct database operations for ir.model and ir.model.fields tables
    """

    @staticmethod
    async def register_model_metadata(
        db_manager, model_name: str, model_schema: Dict[str, Any]
    ) -> bool:
        """
        Register a single model in ir.model table

        Args:
            db_manager: Database manager instance
            model_name: Name of the model to register
            model_schema: Schema definition of the model

        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if model already exists
            exists = await db_manager.fetchval(
                "SELECT EXISTS(SELECT 1 FROM ir_model WHERE model = $1)", model_name
            )

            if exists:
                logger.debug(f"Model {model_name} already exists in ir.model, skipping")
                return True

            # Generate table name from model name
            table_name = model_name.replace(".", "_")

            # Insert model metadata
            model_id = str(uuid.uuid4())
            now = datetime.now()

            await db_manager.execute(
                """
                INSERT INTO ir_model (id, name, model, info, description, table_name_db, state, transient, create_at, update_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            """,
                model_id,
                model_name,  # name
                model_name,  # model
                f"Model for {model_name}",  # info
                f"Auto-generated model metadata for {model_name}",  # description
                table_name,  # table_name_db
                "base",  # state
                False,  # transient
                now,  # create_at
                now,  # update_at
            )

            logger.debug(f"✓ Registered model metadata for {model_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to register model metadata for {model_name}: {e}")
            return False

    @staticmethod
    async def register_model_fields_metadata(
        db_manager, model_name: str, model_schema: Dict[str, Any]
    ) -> int:
        """
        Register model fields in ir.model.fields table

        Args:
            db_manager: Database manager instance
            model_name: Name of the model
            model_schema: Schema definition of the model

        Returns:
            Number of fields registered
        """
        try:
            fields_registered = 0
            fields = model_schema.get("fields", {})

            for field_name, field_info in fields.items():
                try:
                    # Check if field already exists
                    exists = await db_manager.fetchval(
                        "SELECT EXISTS(SELECT 1 FROM ir_model_fields WHERE model = $1 AND name = $2)",
                        model_name,
                        field_name,
                    )

                    if exists:
                        logger.debug(
                            f"Field {model_name}.{field_name} already exists, skipping"
                        )
                        continue

                    # Extract field information
                    field_type = field_info.get("type", "char")
                    field_description = field_info.get(
                        "string", field_name.replace("_", " ").title()
                    )
                    help_text = field_info.get("help", "")
                    required = field_info.get("required", False)
                    readonly = field_info.get("readonly", False)
                    store = field_info.get("store", True)
                    index = field_info.get("index", False)
                    translate = field_info.get("translate", False)
                    size = field_info.get("size")
                    digits = field_info.get("digits")
                    domain = field_info.get("domain", "[]")
                    context = field_info.get("context", "{}")

                    # Convert digits tuple to string if present
                    digits_str = None
                    if (
                        digits
                        and isinstance(digits, (list, tuple))
                        and len(digits) == 2
                    ):
                        digits_str = f"{digits[0]},{digits[1]}"

                    # Convert domain and context to strings if they're not already
                    if not isinstance(domain, str):
                        domain = str(domain)
                    if not isinstance(context, str):
                        context = str(context)

                    # Insert field metadata
                    field_id = str(uuid.uuid4())
                    now = datetime.now()

                    await db_manager.execute(
                        """
                        INSERT INTO ir_model_fields (
                            id, model, name, field_description, help, ttype, required, readonly,
                            store, is_indexed, translate, size, digits, domain, context, state, create_at, update_at
                        ) VALUES (
                            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
                        )
                    """,
                        field_id,
                        model_name,
                        field_name,
                        field_description,
                        help_text,
                        field_type,
                        required,
                        readonly,
                        store,
                        index,
                        translate,
                        size,
                        digits_str,
                        domain,
                        context,
                        "manual",  # state
                        now,  # create_at
                        now,  # update_at
                    )

                    fields_registered += 1
                    logger.debug(f"✓ Registered field {model_name}.{field_name}")

                except Exception as e:
                    logger.error(
                        f"Failed to register field {model_name}.{field_name}: {e}"
                    )
                    continue

            return fields_registered

        except Exception as e:
            logger.error(f"Failed to register fields for model {model_name}: {e}")
            return 0

    @staticmethod
    async def update_model_metadata(
        db_manager, model_name: str, updates: Dict[str, Any]
    ) -> bool:
        """
        Update existing model metadata

        Args:
            db_manager: Database manager instance
            model_name: Name of the model to update
            updates: Dictionary of fields to update

        Returns:
            True if successful, False otherwise
        """
        try:
            # Build update query dynamically
            set_clauses = []
            params = []
            param_count = 1

            for field, value in updates.items():
                set_clauses.append(f"{field} = ${param_count}")
                params.append(value)
                param_count += 1

            # Add update timestamp
            set_clauses.append(f"update_at = ${param_count}")
            params.append(datetime.now())
            param_count += 1

            # Add model name for WHERE clause
            params.append(model_name)

            query = f"""
                UPDATE ir_model 
                SET {', '.join(set_clauses)}
                WHERE model = ${param_count}
            """

            result = await db_manager.execute(query, *params)

            if result:
                logger.debug(f"✓ Updated model metadata for {model_name}")
                return True
            else:
                logger.warning(f"No model found to update: {model_name}")
                return False

        except Exception as e:
            logger.error(f"Failed to update model metadata for {model_name}: {e}")
            return False

    @staticmethod
    async def update_field_metadata(
        db_manager, model_name: str, field_name: str, updates: Dict[str, Any]
    ) -> bool:
        """
        Update existing field metadata

        Args:
            db_manager: Database manager instance
            model_name: Name of the model
            field_name: Name of the field to update
            updates: Dictionary of fields to update

        Returns:
            True if successful, False otherwise
        """
        try:
            # Build update query dynamically
            set_clauses = []
            params = []
            param_count = 1

            for field, value in updates.items():
                set_clauses.append(f"{field} = ${param_count}")
                params.append(value)
                param_count += 1

            # Add update timestamp
            set_clauses.append(f"update_at = ${param_count}")
            params.append(datetime.now())
            param_count += 1

            # Add model and field names for WHERE clause
            params.extend([model_name, field_name])

            query = f"""
                UPDATE ir_model_fields 
                SET {', '.join(set_clauses)}
                WHERE model = ${param_count} AND name = ${param_count + 1}
            """

            result = await db_manager.execute(query, *params)

            if result:
                logger.debug(f"✓ Updated field metadata for {model_name}.{field_name}")
                return True
            else:
                logger.warning(f"No field found to update: {model_name}.{field_name}")
                return False

        except Exception as e:
            logger.error(
                f"Failed to update field metadata for {model_name}.{field_name}: {e}"
            )
            return False

    @staticmethod
    async def delete_model_metadata(db_manager, model_name: str) -> bool:
        """
        Delete model and its fields from IR metadata

        Args:
            db_manager: Database manager instance
            model_name: Name of the model to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete fields first (foreign key constraint)
            await db_manager.execute(
                "DELETE FROM ir_model_fields WHERE model = $1", model_name
            )

            # Delete model
            result = await db_manager.execute(
                "DELETE FROM ir_model WHERE model = $1", model_name
            )

            if result:
                logger.debug(f"✓ Deleted model metadata for {model_name}")
                return True
            else:
                logger.warning(f"No model found to delete: {model_name}")
                return False

        except Exception as e:
            logger.error(f"Failed to delete model metadata for {model_name}: {e}")
            return False

    @staticmethod
    async def get_model_metadata(
        db_manager, model_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get model metadata from ir.model table

        Args:
            db_manager: Database manager instance
            model_name: Name of the model

        Returns:
            Model metadata dictionary or None if not found
        """
        try:
            result = await db_manager.fetchrow(
                "SELECT * FROM ir_model WHERE model = $1", model_name
            )

            return dict(result) if result else None

        except Exception as e:
            logger.error(f"Failed to get model metadata for {model_name}: {e}")
            return None

    @staticmethod
    async def get_field_metadata(
        db_manager, model_name: str, field_name: str = None
    ) -> List[Dict[str, Any]]:
        """
        Get field metadata from ir.model.fields table

        Args:
            db_manager: Database manager instance
            model_name: Name of the model
            field_name: Optional specific field name

        Returns:
            List of field metadata dictionaries
        """
        try:
            if field_name:
                query = "SELECT * FROM ir_model_fields WHERE model = $1 AND name = $2"
                params = [model_name, field_name]
            else:
                query = "SELECT * FROM ir_model_fields WHERE model = $1 ORDER BY name"
                params = [model_name]

            results = await db_manager.fetch(query, *params)

            return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Failed to get field metadata for {model_name}: {e}")
            return []
