"""
HTML error response generator for HTTP routes
Creates enhanced error pages using XML templates with Tailwind CSS support
"""

import traceback
from datetime import datetime
from typing import Any, Dict, Optional

from fastapi.responses import HTMLResponse


class HTMLErrorResponse:
    """Generator for HTML error responses using XML templates"""

    @staticmethod
    def _get_template_manager():
        """Get template manager instance"""
        from ...templates.manager import get_template_manager

        return get_template_manager()

    @staticmethod
    def create_error_page(
        error: Exception,
        status_code: int = 500,
        title: Optional[str] = None,
        include_stacktrace: bool = True,
        request_info: Optional[Dict[str, Any]] = None,
    ) -> HTMLResponse:
        """
        Create an HTML error page using XML templates

        Args:
            error: The exception that occurred
            status_code: HTTP status code
            title: Custom title for the error page
            include_stacktrace: Whether to include stacktrace
            request_info: Additional request information to display

        Returns:
            HTMLResponse with error page
        """
        template_manager = HTMLErrorResponse._get_template_manager()

        # Prepare template context
        error_title = title or f"Error {status_code}"
        error_message = str(error) or "An error occurred"
        error_type = type(error).__name__
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Generate stacktrace if requested
        stacktrace = None
        if include_stacktrace:
            stacktrace_lines = traceback.format_exception(
                type(error), error, error.__traceback__
            )
            stacktrace = "".join(stacktrace_lines)

        context = {
            "title": error_title,
            "error_message": error_message,
            "error_type": error_type,
            "status_code": status_code,
            "timestamp": timestamp,
            "stacktrace": stacktrace,
            "include_stacktrace": include_stacktrace,
            "request_info": request_info or {},
        }

        # Render using template engine
        html_content = template_manager.render_template("error.detailed", context)
        return HTMLResponse(content=html_content, status_code=status_code)

    @staticmethod
    def create_simple_error_page(
        message: str, status_code: int = 500, title: Optional[str] = None
    ) -> HTMLResponse:
        """
        Create a simple HTML error page without stacktrace using XML templates

        Args:
            message: Error message to display
            status_code: HTTP status code
            title: Custom title for the error page

        Returns:
            HTMLResponse with simple error page
        """
        template_manager = HTMLErrorResponse._get_template_manager()

        # Prepare template context
        error_title = title or f"Error {status_code}"
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        context = {
            "title": error_title,
            "error_message": message,
            "status_code": status_code,
            "timestamp": timestamp,
        }

        # Render using template engine
        html_content = template_manager.render_template("error.simple", context)
        return HTMLResponse(content=html_content, status_code=status_code)
