"""
Hook registry implementation

This module provides the central registry for managing addon lifecycle hooks.
"""

import logging
import time
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional

if TYPE_CHECKING:
    from ...environment import Environment
else:
    Environment = "Environment"

from ...logging import get_logger
from ...logging.coordination import operation_context
from .addon_hook import <PERSON>don<PERSON><PERSON>
from .hook_context import HookContext
from .hook_types import HookType

logger = get_logger(__name__)


class HookRegistry:
    """Registry for managing addon lifecycle hooks"""

    # Type annotations for instance attributes
    _hooks: Dict[HookType, List[AddonHook]]

    def __init__(self) -> None:
        self._hooks: Dict[HookType, List[AddonHook]] = {
            hook_type: [] for hook_type in HookType
        }

    def register_hook(
        self,
        func: Callable[[HookContext], Any],
        hook_type: HookType,
        addon_name: str,
        priority: int = 50,
    ) -> None:
        """Register a lifecycle hook"""
        # Check for duplicate hook registration (same function, type, addon, priority)
        func_name = getattr(func, "__name__", str(func))
        for existing_hook in self._hooks[hook_type]:
            if (
                existing_hook.addon_name == addon_name
                and existing_hook.priority == priority
                and getattr(existing_hook.func, "__name__", str(existing_hook.func))
                == func_name
            ):
                logger.debug(
                    f"Skipping duplicate {hook_type.value} hook registration for {addon_name}:{func_name}"
                )
                return

        hook = AddonHook(func, hook_type, addon_name, priority)
        self._hooks[hook_type].append(hook)

        # Sort hooks by priority (lower number = higher priority)
        self._hooks[hook_type].sort(key=lambda h: h.priority)

        # Only log hook registration in debug mode and avoid duplicates
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(
                f"Registered {hook_type.value} hook for {addon_name} (total: {len(self._hooks[hook_type])})"
            )

    def unregister_addon_hooks(self, addon_name: str) -> None:
        """Remove all hooks for a specific addon"""
        for hook_type in HookType:
            self._hooks[hook_type] = [
                hook for hook in self._hooks[hook_type] if hook.addon_name != addon_name
            ]
        logger.debug(f"Unregistered all hooks for addon {addon_name}")

    def get_hooks(self, hook_type: HookType) -> List[AddonHook]:
        """Get all hooks for a specific type"""
        return self._hooks[hook_type].copy()

    async def execute_hooks(
        self,
        hook_type: HookType,
        addon_name: str,
        env: Optional["Environment"] = None,
        **kwargs: Any,
    ) -> List[Any]:
        """Execute all hooks of a specific type"""
        operation_id = f"hooks_{hook_type.value}_{addon_name}"

        # Use operation context to prevent duplicate executions
        with operation_context(
            operation_id, hook_type=hook_type.value, addon=addon_name
        ) as should_execute:
            if not should_execute:
                logger.debug(
                    f"Skipping duplicate {hook_type.value} hooks execution for {addon_name}"
                )
                return []

            hooks = self.get_hooks(hook_type)
            context = HookContext(addon_name, hook_type, env, **kwargs)
            results = []

            if hooks:
                logger.info(
                    f"Executing {len(hooks)} {hook_type.value} hooks for addon {addon_name}"
                )
                start_time = time.perf_counter()

                for i, hook in enumerate(hooks, 1):
                    try:
                        result = await hook.execute(context)
                        results.append(result)
                    except Exception as e:
                        logger.error(f"Hook {i}/{len(hooks)} execution failed: {e}")
                        # Continue executing other hooks even if one fails
                        results.append(None)

                total_duration = time.perf_counter() - start_time
                logger.info(
                    f"Completed {len(hooks)} {hook_type.value} hooks for {addon_name} in {total_duration:.3f}s"
                )
            else:
                logger.debug(f"No {hook_type.value} hooks found for addon {addon_name}")

            return results


# Global hook registry
_hook_registry = HookRegistry()


def get_hook_registry() -> HookRegistry:
    """Get the global hook registry"""
    return _hook_registry
