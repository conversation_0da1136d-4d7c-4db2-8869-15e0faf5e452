"""
Registry Refresh Coordinator

This module coordinates registry refreshes to ensure they only happen once
after the entire addon lifecycle completes, rather than after each individual addon.
"""

import asyncio
from typing import TYPE_CHECKING, Dict, Optional, Set

if TYPE_CHECKING:
    from .lifecycle_tracker import InstallationSession

from ...logging import get_logger

logger = get_logger(__name__)


class RegistryRefreshCoordinator:
    """
    Coordinates registry refreshes to happen only once per installation session.

    This coordinator:
    1. Intercepts individual registry refresh requests
    2. Queues them for batch processing
    3. Triggers a single refresh when the installation session completes
    4. Handles transaction completion detection
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self._pending_refreshes: Dict[str, Set[str]] = (
            {}
        )  # db_name -> set of module_names
        self._refresh_locks: Dict[str, asyncio.Lock] = {}  # db_name -> lock
        self._lock = asyncio.Lock()

    async def queue_registry_refresh(
        self,
        db_name: str,
        module_name: str,
        action: str,
        session_id: Optional[str] = None,
    ):
        """
        Queue a registry refresh instead of executing it immediately.

        Args:
            db_name: Database name
            module_name: Module name that triggered the refresh
            action: Action performed (install/uninstall/upgrade)
            session_id: Installation session ID if available
        """
        async with self._lock:
            if db_name not in self._pending_refreshes:
                self._pending_refreshes[db_name] = set()

            self._pending_refreshes[db_name].add(module_name)

            self.logger.debug(
                f"Queued registry refresh for database {db_name} after {action} of module {module_name}"
            )

            if session_id:
                self.logger.debug(f"Refresh queued as part of session {session_id}")
            else:
                # If no session, this might be a standalone operation - refresh immediately
                self.logger.debug(
                    "No session ID provided - this might be a standalone operation"
                )
                await self._execute_immediate_refresh(db_name, module_name, action)

    async def _execute_immediate_refresh(
        self, db_name: str, module_name: str, action: str
    ):
        """Execute immediate refresh for standalone operations"""
        try:
            # Small delay to allow transaction to complete
            await asyncio.sleep(0.1)

            from ...database.memory.registry_manager import MemoryRegistryManager

            self.logger.info(
                f"Executing immediate registry refresh for database {db_name} after {action} of module {module_name}"
            )
            success = await MemoryRegistryManager.update_registry_after_module_action(
                db_name, module_name, action
            )

            if success:
                self.logger.debug(
                    f"✅ Immediate registry refresh successful for database {db_name}"
                )
                # Clear from pending refreshes
                async with self._lock:
                    if (
                        db_name in self._pending_refreshes
                        and module_name in self._pending_refreshes[db_name]
                    ):
                        self._pending_refreshes[db_name].discard(module_name)
                        if not self._pending_refreshes[db_name]:
                            del self._pending_refreshes[db_name]
            else:
                self.logger.warning(
                    f"⚠️ Immediate registry refresh failed for database {db_name}"
                )

        except Exception as e:
            self.logger.error(
                f"❌ Error in immediate registry refresh for database {db_name}: {e}"
            )

    async def execute_session_completion_refresh(self, session: "InstallationSession"):
        """
        Execute registry refresh when an installation session completes.

        Args:
            session: The completed installation session
        """
        db_name = session.db_name

        # Get database-specific lock
        async with self._lock:
            if db_name not in self._refresh_locks:
                self._refresh_locks[db_name] = asyncio.Lock()
            refresh_lock = self._refresh_locks[db_name]

        async with refresh_lock:
            try:
                # Check if there are pending refreshes for this database
                pending_modules = self._pending_refreshes.get(db_name, set())

                if not pending_modules:
                    self.logger.debug(
                        f"No pending registry refreshes for database {db_name}"
                    )
                    return

                self.logger.info(
                    f"Executing session completion registry refresh for database {db_name}"
                )
                self.logger.debug(
                    f"Session {session.session_id} completed with modules: {session.completed_addons}"
                )
                self.logger.debug(f"Pending refreshes for modules: {pending_modules}")

                # Add a small delay to ensure transaction is fully committed
                await asyncio.sleep(0.2)

                # Execute the registry refresh
                from ...database.memory.registry_manager import MemoryRegistryManager

                # We'll refresh the entire registry since multiple modules were involved
                success = await MemoryRegistryManager.refresh_registry(db_name)

                if success:
                    self.logger.info(
                        f"✅ Session completion registry refresh successful for database {db_name}"
                    )
                    self.logger.debug(
                        f"Registry refreshed for {len(pending_modules)} modules: {pending_modules}"
                    )

                    # Clear pending refreshes for this database
                    async with self._lock:
                        if db_name in self._pending_refreshes:
                            del self._pending_refreshes[db_name]
                else:
                    self.logger.error(
                        f"❌ Session completion registry refresh failed for database {db_name}"
                    )

            except Exception as e:
                self.logger.error(
                    f"❌ Error in session completion registry refresh for database {db_name}: {e}"
                )

    async def get_pending_refreshes(
        self, db_name: Optional[str] = None
    ) -> Dict[str, Set[str]]:
        """
        Get pending registry refreshes.

        Args:
            db_name: Specific database name, or None for all databases

        Returns:
            Dictionary of pending refreshes
        """
        async with self._lock:
            if db_name:
                return {db_name: self._pending_refreshes.get(db_name, set())}
            else:
                return self._pending_refreshes.copy()

    async def clear_pending_refreshes(self, db_name: str):
        """Clear pending refreshes for a database"""
        async with self._lock:
            if db_name in self._pending_refreshes:
                del self._pending_refreshes[db_name]
                self.logger.debug(f"Cleared pending refreshes for database {db_name}")

    async def get_status(self) -> Dict[str, any]:
        """Get coordinator status for debugging"""
        async with self._lock:
            return {
                "pending_refreshes": {
                    db: list(modules) for db, modules in self._pending_refreshes.items()
                },
                "active_locks": list(self._refresh_locks.keys()),
                "total_pending_databases": len(self._pending_refreshes),
            }


# Singleton instance
_refresh_coordinator = None


def get_refresh_coordinator() -> RegistryRefreshCoordinator:
    """Get the global registry refresh coordinator instance"""
    global _refresh_coordinator
    if _refresh_coordinator is None:
        _refresh_coordinator = RegistryRefreshCoordinator()
    return _refresh_coordinator
