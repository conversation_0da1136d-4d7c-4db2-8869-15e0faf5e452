"""
ERP CLI Lint Commands - Code quality and formatting tools
"""

import argparse
import subprocess
import sys
from pathlib import Path
from typing import List

from .base import BaseCommand, CommandGroup


class LintCommand(BaseCommand):
    """Run code linting and style checks"""

    def add_arguments(self, parser: argparse.ArgumentParser):
        """Add lint command arguments"""
        parser.add_argument(
            "--fix", action="store_true", help="Automatically fix issues where possible"
        )
        parser.add_argument(
            "--tool",
            choices=["flake8", "isort", "black", "mypy", "all"],
            default="all",
            help="Specific tool to run (default: all)",
        )
        parser.add_argument(
            "--paths",
            nargs="*",
            default=["erp/", "tests/", "examples/", "scripts/"],
            help="Paths to lint (default: erp/ tests/ examples/ scripts/)",
        )
        parser.add_argument(
            "--check-only",
            action="store_true",
            help="Only check, do not fix (opposite of --fix)",
        )

    def handle(self, args: argparse.Namespace) -> int:
        """Handle lint command execution"""
        try:
            # Determine if we should fix issues
            should_fix = args.fix and not args.check_only

            if should_fix:
                self.print_info("🔧 Running linting with automatic fixes...")
            else:
                self.print_info("🔍 Running linting checks...")

            # Get paths to lint
            paths = args.paths

            # Validate paths exist
            valid_paths = []
            for path in paths:
                if Path(path).exists():
                    valid_paths.append(path)
                else:
                    self.print_warning(f"Path does not exist: {path}")

            if not valid_paths:
                self.print_error("No valid paths to lint")
                return 1

            # Run linting tools
            total_issues = 0

            if args.tool in ["isort", "all"]:
                issues = self._run_isort(valid_paths, should_fix)
                total_issues += issues

            if args.tool in ["black", "all"]:
                issues = self._run_black(valid_paths, should_fix)
                total_issues += issues

            if args.tool in ["flake8", "all"]:
                issues = self._run_flake8(valid_paths)
                total_issues += issues

            if args.tool in ["mypy", "all"]:
                issues = self._run_mypy(valid_paths)
                total_issues += issues

            # Summary
            if total_issues == 0:
                self.print_success("✨ All linting checks passed!")
                return 0
            else:
                if should_fix:
                    self.print_info(f"🔧 Fixed {total_issues} issues")
                    # Run checks again to see remaining issues
                    self.print_info("🔍 Running final checks...")
                    remaining_issues = 0
                    if args.tool in ["flake8", "all"]:
                        remaining_issues += self._run_flake8(valid_paths)
                    if args.tool in ["mypy", "all"]:
                        remaining_issues += self._run_mypy(valid_paths)

                    if remaining_issues == 0:
                        self.print_success("✨ All issues fixed!")
                        return 0
                    else:
                        self.print_warning(
                            f"⚠️ {remaining_issues} issues remain (manual fix required)"
                        )
                        return 1
                else:
                    self.print_error(f"❌ Found {total_issues} issues")
                    self.print_info(
                        "💡 Run with --fix to automatically fix some issues"
                    )
                    return 1

        except Exception as e:
            self.print_error(f"Linting failed: {e}")
            return 1

    def _run_command(self, cmd: List[str], description: str) -> tuple[int, str, str]:
        """Run a command and return exit code, stdout, stderr"""
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding="utf-8",
                errors="replace",
                timeout=300,  # 5 minute timeout
            )
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            self.print_error(f"Timeout running {description}")
            return 1, "", "Timeout"
        except FileNotFoundError:
            self.print_error(f"Tool not found for {description}")
            return 1, "", "Tool not found"
        except UnicodeDecodeError:
            self.print_error(f"Unicode decode error running {description}")
            return 1, "", "Unicode decode error"

    def _run_isort(self, paths: List[str], should_fix: bool) -> int:
        """Run isort for import sorting"""
        self.print_info("📦 Checking import sorting with isort...")

        cmd = [sys.executable, "-m", "isort"] + paths
        if not should_fix:
            cmd.extend(["--check-only", "--diff"])

        exit_code, stdout, stderr = self._run_command(cmd, "isort")

        if exit_code != 0:
            if should_fix:
                self.print_info("🔧 Fixed import sorting issues")
            else:
                self.print_error("❌ Import sorting issues found")
                if stdout:
                    print(stdout)
            return 1
        else:
            self.print_success("✓ Import sorting is correct")
            return 0

    def _run_black(self, paths: List[str], should_fix: bool) -> int:
        """Run black for code formatting"""
        self.print_info("🎨 Checking code formatting with black...")

        cmd = [sys.executable, "-m", "black"] + paths
        if not should_fix:
            cmd.extend(["--check", "--diff"])

        exit_code, stdout, stderr = self._run_command(cmd, "black")

        if exit_code != 0:
            if should_fix:
                self.print_info("🔧 Fixed code formatting issues")
            else:
                self.print_error("❌ Code formatting issues found")
                if stdout:
                    print(stdout)
            return 1
        else:
            self.print_success("✓ Code formatting is correct")
            return 0

    def _run_flake8(self, paths: List[str]) -> int:
        """Run flake8 for style and error checking"""
        self.print_info("🔍 Checking style and errors with flake8...")

        cmd = [sys.executable, "-m", "flake8"] + paths
        exit_code, stdout, stderr = self._run_command(cmd, "flake8")

        if exit_code != 0:
            self.print_error("❌ Flake8 issues found")
            if stdout:
                print(stdout)
            return 1
        else:
            self.print_success("✓ No flake8 issues found")
            return 0

    def _run_mypy(self, paths: List[str]) -> int:
        """Run mypy for type checking"""
        self.print_info("🔬 Checking types with mypy...")

        # Only check Python files in erp/ directory for now
        python_paths = [p for p in paths if p.startswith("erp/")]
        if not python_paths:
            self.print_info("ℹ️ Skipping mypy (no erp/ paths specified)")
            return 0

        cmd = [sys.executable, "-m", "mypy"] + python_paths
        exit_code, stdout, stderr = self._run_command(cmd, "mypy")

        if exit_code != 0:
            self.print_error("❌ Type checking issues found")
            if stdout:
                print(stdout)
            return 1
        else:
            self.print_success("✓ No type checking issues found")
            return 0


class LintCommandGroup(CommandGroup):
    """Lint command group for code quality tools"""

    def __init__(self):
        super().__init__()
        self.register_command(LintCommand())

    def add_commands(self, subparsers, parent_parser=None):
        """Add lint commands to subparsers"""
        # Lint command
        lint_parser = subparsers.add_parser(
            "lint",
            help="Run code linting and formatting checks",
            parents=[parent_parser] if parent_parser else [],
        )
        self.commands["lint"].add_arguments(lint_parser)
