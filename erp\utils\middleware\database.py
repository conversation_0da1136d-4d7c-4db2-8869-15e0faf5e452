import logging
from typing import Any, Callable, Optional, Set

from fastapi import HTTPException, Request, Response

from ...config import config
from ...database.memory import DatabaseFilterProcessor
from ...database.memory.registry_manager import MemoryRegistryManager
from ...database.registry import DatabaseRegistry
from ...http.registries import get_system_route_registry
from ..core.responses import handle_database_error

logger = logging.getLogger(__name__)


class RouteClassifier:
    """Simplified route classifier for determining database middleware requirements."""

    # Cache for system routes to avoid repeated registry calls
    _cached_system_routes: Optional[Set[str]] = None

    @classmethod
    def _get_system_routes(cls) -> Set[str]:
        """Get system routes from registry and configuration with caching."""
        if cls._cached_system_routes is None:
            cls._cached_system_routes = set()

            # Load routes from registry
            try:
                system_registry = get_system_route_registry()
                registry_routes = system_registry.get_system_route_paths()
                cls._cached_system_routes.update(registry_routes)
                logger.debug(
                    f"Loaded {len(registry_routes)} system routes from registry"
                )
            except Exception as e:
                logger.error(f"Failed to load system routes from registry: {e}")

            # Load routes from configuration
            try:
                config_routes = config.system_routes
                cls._cached_system_routes.update(config_routes)
                logger.debug(
                    f"Loaded {len(config_routes)} system routes from configuration"
                )
            except Exception as e:
                logger.error(f"Failed to load system routes from configuration: {e}")

            logger.debug(
                f"Total system routes loaded: {len(cls._cached_system_routes)}"
            )
        return cls._cached_system_routes

    @classmethod
    def clear_cache(cls):
        """Clear cached routes. Useful for testing or when routes change."""
        cls._cached_system_routes = None

    @classmethod
    def needs_database_middleware(cls, path: str) -> bool:
        """
        Determine if a path needs database middleware.

        Args:
            path: URL path to check

        Returns:
            bool: True if database middleware is needed, False otherwise
        """
        # Get system routes from registry
        system_routes = cls._get_system_routes()

        # Check exact matches for system routes
        if path in system_routes:
            return False

        # Special case: static files are handled by systemRoute but need prefix matching
        if path.startswith("/static/"):
            return False

        # All other routes need database middleware (including addon routes like /home)
        return True


class DatabaseMiddleware:
    """Simplified database middleware for handling database context in requests."""

    @staticmethod
    def should_skip(path: str) -> bool:
        """Check if the given path should skip database middleware."""
        needs_db = RouteClassifier.needs_database_middleware(path)

        if not needs_db:
            logger.debug(f"⏭️ Skipping database middleware for system path: {path}")
            return True
        else:
            logger.debug(f"✅ Database middleware required for path: {path}")
            return False

    @staticmethod
    def _get_database_name(request: Request) -> tuple[str | None, bool]:
        """Get database name from request (query, header, or cookie)."""
        # Priority: query param > header > cookie
        db_name = request.query_params.get("db")
        should_set_cookie = bool(db_name)  # Set cookie if from query param

        if not db_name:
            db_name = request.headers.get("X-Database")

        if not db_name:
            db_name = request.cookies.get("erp_database")

        return db_name, should_set_cookie

    @staticmethod
    def _validate_database_name(db_name: str) -> None:
        """Validate database name against configured filters."""
        if not DatabaseFilterProcessor.check_database_matches_filter(
            db_name, config.db_filter
        ):
            raise HTTPException(
                status_code=400,
                detail={
                    "error": f"Database '{db_name}' does not match configured filter",
                    "requested_database": db_name,
                    "configured_filter": config.db_filter,
                },
            )

    @staticmethod
    async def _check_database_exists(db_name: str) -> None:
        """Check if database exists."""
        available_databases = await DatabaseRegistry.list_databases()
        if db_name not in available_databases:
            raise HTTPException(
                status_code=404,
                detail={
                    "error": f"Database '{db_name}' not found",
                    "available_databases": available_databases,
                },
            )

    @staticmethod
    async def _initialize_registry(db_name: str) -> None:
        """Initialize database memory registry only if not already initialized."""
        try:
            # Check if registry is fully initialized (exists, routes registered, addons loaded)
            if await MemoryRegistryManager.is_registry_fully_initialized(db_name):
                logger.debug(
                    f"Using existing fully initialized database registry: {db_name}"
                )
                return

            # Check if registry exists but needs route registration
            if await MemoryRegistryManager.has_registry(db_name):
                registry = await MemoryRegistryManager.get_registry(db_name)
                # Ensure routes are registered if not already done
                if not registry.route_manager._routes_registered:
                    await registry.route_manager.ensure_routes_registered()
                logger.debug(
                    f"Completed initialization for existing database registry: {db_name}"
                )
                return

            # Only initialize if registry doesn't exist at all
            await MemoryRegistryManager.initialize_registry_with_delay(
                db_name, delay_seconds=0.0
            )
            logger.debug(f"Database registry initialized from scratch: {db_name}")
        except Exception as e:
            logger.error(f"Failed to initialize registry for {db_name}: {e}")
            raise HTTPException(
                status_code=503,
                detail={
                    "error": "Database registry initialization failed",
                    "database": db_name,
                    "message": str(e),
                },
            )

    @staticmethod
    async def _is_addon_route_requiring_database(path: str) -> bool:
        """
        Check if a path is an addon route that requires database context.
        This helps provide better error messages for routes that need database context.
        """
        # Common addon routes that require database context
        addon_routes = [
            "/home",
            "/web",
            "/api",
            "/admin",
            "/dashboard",
            "/reports",
            "/settings",
        ]

        # Check if path starts with any known addon route
        for addon_route in addon_routes:
            if path.startswith(addon_route):
                return True

        # Check if path looks like an addon route (not a system route)
        system_routes = RouteClassifier._get_system_routes()
        if path in system_routes or path.startswith("/static/"):
            return False

        # If it's not a system route and not static, it's likely an addon route
        return True

    @staticmethod
    async def process_request(request: Request, call_next: Callable) -> Any:
        """Process request with database context."""
        # Skip database middleware for system routes
        if DatabaseMiddleware.should_skip(request.url.path):
            return await call_next(request)

        # Determine database name
        if config.is_single_db_mode:
            db_name = config.get_default_database()
            should_set_cookie = False
        else:
            db_name, should_set_cookie = DatabaseMiddleware._get_database_name(request)
            if db_name:
                DatabaseMiddleware._validate_database_name(db_name)

        # Validate database name is provided
        if not db_name:
            if config.is_single_db_mode:
                raise HTTPException(
                    status_code=500,
                    detail={"error": "No database configured in single database mode"},
                )
            else:
                # In multi-database mode, if no database is specified,
                # check if this is an addon route that needs database context
                path = request.url.path
                if await DatabaseMiddleware._is_addon_route_requiring_database(path):
                    # Return a helpful error suggesting database selection
                    from fastapi.responses import HTMLResponse

                    error_html = f"""
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Database Required</title>
                        <style>
                            body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
                            .container {{ background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto; }}
                            h1 {{ color: #d32f2f; margin-bottom: 20px; }}
                            .suggestion {{ background: #e3f2fd; padding: 15px; border-radius: 4px; margin: 20px 0; }}
                            .code {{ background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; }}
                            a {{ color: #1976d2; text-decoration: none; }}
                            a:hover {{ text-decoration: underline; }}
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <h1>Database Required</h1>
                            <p>The route <strong>{path}</strong> requires a database context, but no database was specified.</p>

                            <div class="suggestion">
                                <strong>How to specify a database:</strong>
                                <ul>
                                    <li>Add <span class="code">?db=database_name</span> to the URL</li>
                                    <li>Set the <span class="code">X-Database</span> header</li>
                                    <li>Use the <span class="code">erp_database</span> cookie</li>
                                </ul>
                            </div>

                            <p>Example: <a href="{path}?db=your_database_name">{path}?db=your_database_name</a></p>

                            <p><a href="/databases">← View available databases</a></p>
                        </div>
                    </body>
                    </html>
                    """
                    return HTMLResponse(content=error_html, status_code=400)
                else:
                    # For non-addon routes, return the original error
                    raise HTTPException(
                        status_code=400,
                        detail={
                            "error": "No database specified",
                            "suggestion": "Use ?db=database_name, X-Database header, or erp_database cookie",
                        },
                    )

        # Check database exists and initialize registry
        await DatabaseMiddleware._check_database_exists(db_name)
        await DatabaseMiddleware._initialize_registry(db_name)

        # Set database context
        DatabaseRegistry.set_current_database(db_name)
        request.state.db_name = db_name
        request.state.should_set_db_cookie = should_set_cookie

        try:
            response = await call_next(request)

            # Set database cookie if needed
            if should_set_cookie and isinstance(response, Response):
                response.set_cookie(
                    key="erp_database",
                    value=db_name,
                    max_age=30 * 24 * 60 * 60,
                    httponly=True,
                    secure=False,
                    samesite="lax",
                    path="/",
                )

            return response
        except Exception as e:
            logger.error(f"Database middleware error: {e}")
            raise handle_database_error(e)
