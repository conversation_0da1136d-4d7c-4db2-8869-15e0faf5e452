"""
JSON RPC 2.0 implementation for ERP HTTP routes
"""

import json
import traceback
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union

from fastapi import Request
from fastapi.responses import JSONResponse

from ...logging import get_logger

logger = get_logger(__name__)


@dataclass
class JsonRpcRequest:
    """JSON RPC 2.0 request structure"""

    jsonrpc: str = "2.0"
    method: str = ""
    params: Optional[Union[Dict[str, Any], List[Any]]] = None
    id: Optional[Union[str, int]] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "JsonRpcRequest":
        """Create request from dictionary"""
        return cls(
            jsonrpc=data.get("jsonrpc", "2.0"),
            method=data.get("method", ""),
            params=data.get("params"),
            id=data.get("id"),
        )

    def is_notification(self) -> bool:
        """Check if this is a notification (no id)"""
        return self.id is None

    def validate(self) -> Optional[str]:
        """Validate the request, return error message if invalid"""
        if self.jsonrpc != "2.0":
            return "Invalid jsonrpc version"
        if not self.method:
            return "Missing method"
        return None


@dataclass
class JsonRpcResponse:
    """JSON RPC 2.0 response structure"""

    jsonrpc: str = "2.0"
    result: Optional[Any] = None
    error: Optional[Dict[str, Any]] = None
    id: Optional[Union[str, int]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        response = {"jsonrpc": self.jsonrpc}
        if self.result is not None:
            response["result"] = self.result
        if self.error is not None:
            response["error"] = self.error
        if self.id is not None:
            response["id"] = self.id
        return response

    def to_response(self) -> JSONResponse:
        """Convert to FastAPI JSONResponse"""
        status_code = 200
        if self.error:
            # Map JSON RPC error codes to HTTP status codes
            error_code = self.error.get("code", 0)
            if error_code == -32700:  # Parse error
                status_code = 400
            elif error_code == -32600:  # Invalid request
                status_code = 400
            elif error_code == -32601:  # Method not found
                status_code = 404
            elif error_code == -32602:  # Invalid params
                status_code = 400
            elif error_code == -32603:  # Internal error
                status_code = 500
            else:
                status_code = 500

        return JSONResponse(content=self.to_dict(), status_code=status_code)


class JsonRpcError:
    """JSON RPC 2.0 error codes and utilities"""

    # Standard error codes
    PARSE_ERROR = -32700
    INVALID_REQUEST = -32600
    METHOD_NOT_FOUND = -32601
    INVALID_PARAMS = -32602
    INTERNAL_ERROR = -32603

    @staticmethod
    def create_error(
        code: int,
        message: str,
        data: Any = None,
        request_id: Any = None,
        include_stacktrace: bool = True,
        exception: Exception = None,
    ) -> JsonRpcResponse:
        """Create an error response with optional stacktrace"""
        error = {"code": code, "message": message}

        # Build error data
        error_data = {}
        if data is not None:
            if isinstance(data, dict):
                error_data.update(data)
            else:
                error_data["details"] = data

        # Add stacktrace if requested
        if include_stacktrace:
            if exception:
                error_data["stacktrace"] = traceback.format_exception(
                    type(exception), exception, exception.__traceback__
                )
                error_data["exception_type"] = type(exception).__name__
            else:
                # Get current stacktrace (excluding this function)
                error_data["stacktrace"] = traceback.format_stack()[:-1]

        if error_data:
            error["data"] = error_data

        return JsonRpcResponse(error=error, id=request_id)

    @classmethod
    def parse_error(
        cls, data: Any = None, request_id: Any = None, include_stacktrace: bool = True
    ) -> JsonRpcResponse:
        """Parse error response"""
        return cls.create_error(
            cls.PARSE_ERROR, "Parse error", data, request_id, include_stacktrace
        )

    @classmethod
    def invalid_request(
        cls, data: Any = None, request_id: Any = None, include_stacktrace: bool = True
    ) -> JsonRpcResponse:
        """Invalid request response"""
        return cls.create_error(
            cls.INVALID_REQUEST, "Invalid Request", data, request_id, include_stacktrace
        )

    @classmethod
    def method_not_found(
        cls, method: str = "", request_id: Any = None, include_stacktrace: bool = False
    ) -> JsonRpcResponse:
        """Method not found response"""
        message = f"Method not found: {method}" if method else "Method not found"
        return cls.create_error(
            cls.METHOD_NOT_FOUND, message, None, request_id, include_stacktrace
        )

    @classmethod
    def invalid_params(
        cls, data: Any = None, request_id: Any = None, include_stacktrace: bool = False
    ) -> JsonRpcResponse:
        """Invalid params response"""
        return cls.create_error(
            cls.INVALID_PARAMS, "Invalid params", data, request_id, include_stacktrace
        )

    @classmethod
    def internal_error(
        cls,
        message: str = "Internal error",
        data: Any = None,
        request_id: Any = None,
        include_stacktrace: bool = True,
        exception: Exception = None,
    ) -> JsonRpcResponse:
        """Internal error response with stacktrace"""
        return cls.create_error(
            cls.INTERNAL_ERROR, message, data, request_id, include_stacktrace, exception
        )

    @classmethod
    def from_exception(
        cls,
        exception: Exception,
        request_id: Any = None,
        include_stacktrace: bool = True,
    ) -> JsonRpcResponse:
        """Create error response from an exception"""
        message = str(exception) or "An error occurred"
        return cls.create_error(
            cls.INTERNAL_ERROR, message, None, request_id, include_stacktrace, exception
        )


class JsonRpcHandler:
    """JSON RPC 2.0 request handler"""

    def __init__(self):
        self._methods: Dict[str, callable] = {}

    def register_method(self, name: str, method: callable):
        """Register a JSON RPC method"""
        self._methods[name] = method
        logger.debug(f"Registered JSON RPC method: {name}")

    def unregister_method(self, name: str):
        """Unregister a JSON RPC method"""
        if name in self._methods:
            del self._methods[name]
            logger.debug(f"Unregistered JSON RPC method: {name}")

    def get_methods(self) -> List[str]:
        """Get list of registered method names"""
        return list(self._methods.keys())

    async def handle_request(self, request: Request) -> JSONResponse:
        """Handle a JSON RPC request"""
        try:
            # Parse request body
            body = await request.body()
            if not body:
                return JsonRpcError.invalid_request(
                    data="Empty request body"
                ).to_response()

            try:
                data = json.loads(body.decode("utf-8"))
            except json.JSONDecodeError as e:
                return JsonRpcError.parse_error(data=str(e)).to_response()

            # Handle batch requests
            if isinstance(data, list):
                return await self._handle_batch_request(data, request)
            else:
                return await self._handle_single_request(data, request)

        except Exception as e:
            logger.error(f"JSON RPC handler error: {e}")
            logger.error(traceback.format_exc())
            return JsonRpcError.from_exception(e).to_response()

    async def _handle_batch_request(
        self, batch: List[Dict], request: Request
    ) -> JSONResponse:
        """Handle batch JSON RPC request"""
        if not batch:
            return JsonRpcError.invalid_request(data="Empty batch").to_response()

        responses = []
        for item in batch:
            response = await self._handle_single_request(item, request)
            # Only add non-notification responses
            if response.status_code != 204:  # Not a notification
                responses.append(response.body.decode("utf-8"))

        if not responses:
            # All were notifications
            return JSONResponse(content=None, status_code=204)

        # Combine responses
        batch_response = "[" + ",".join(responses) + "]"
        return JSONResponse(content=json.loads(batch_response))

    async def _handle_single_request(
        self, data: Dict, request: Request
    ) -> JSONResponse:
        """Handle single JSON RPC request"""
        # Parse request
        try:
            rpc_request = JsonRpcRequest.from_dict(data)
        except Exception as e:
            return JsonRpcError.invalid_request(data=str(e)).to_response()

        # Validate request
        validation_error = rpc_request.validate()
        if validation_error:
            return JsonRpcError.invalid_request(
                data=validation_error, request_id=rpc_request.id
            ).to_response()

        # Check if method exists
        if rpc_request.method not in self._methods:
            if rpc_request.is_notification():
                return JSONResponse(content=None, status_code=204)
            return JsonRpcError.method_not_found(
                rpc_request.method, rpc_request.id
            ).to_response()

        # Call method
        try:
            method = self._methods[rpc_request.method]

            # Prepare parameters
            if rpc_request.params is None:
                args = []
                kwargs = {}
            elif isinstance(rpc_request.params, list):
                args = rpc_request.params
                kwargs = {}
            elif isinstance(rpc_request.params, dict):
                args = []
                kwargs = rpc_request.params
            else:
                if rpc_request.is_notification():
                    return JSONResponse(content=None, status_code=204)
                return JsonRpcError.invalid_params(
                    request_id=rpc_request.id
                ).to_response()

            # Add request to kwargs if method expects it
            import inspect

            sig = inspect.signature(method)
            if "request" in sig.parameters:
                kwargs["request"] = request

            # Call method
            if inspect.iscoroutinefunction(method):
                result = await method(*args, **kwargs)
            else:
                result = method(*args, **kwargs)

            # Handle notification (no response)
            if rpc_request.is_notification():
                return JSONResponse(content=None, status_code=204)

            # Return success response
            return JsonRpcResponse(result=result, id=rpc_request.id).to_response()

        except Exception as e:
            logger.error(f"Error calling JSON RPC method {rpc_request.method}: {e}")
            logger.error(traceback.format_exc())

            if rpc_request.is_notification():
                return JSONResponse(content=None, status_code=204)

            return JsonRpcError.from_exception(
                e, request_id=rpc_request.id
            ).to_response()


# Global JSON RPC handler
_jsonrpc_handler = JsonRpcHandler()


def get_jsonrpc_handler() -> JsonRpcHandler:
    """Get the global JSON RPC handler"""
    return _jsonrpc_handler


def jsonrpc_method(name: str = None):
    """Decorator to register a function as a JSON RPC method"""

    def decorator(func):
        method_name = name or func.__name__
        _jsonrpc_handler.register_method(method_name, func)
        return func

    return decorator
