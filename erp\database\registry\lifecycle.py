"""
Database lifecycle management (create, drop operations)
"""

import time

from ...logging import get_logger
from .database_registry import DatabaseRegistry
from .initialization import DatabaseInitializer


class DatabaseLifecycle:
    """Database lifecycle management"""

    _logger = get_logger(__name__)

    @classmethod
    async def create_database(cls, db_name: str) -> bool:
        """Create a new database and initialize it with base addon"""
        cls._logger.info(f"Creating database: {db_name}")
        start_time = time.perf_counter()

        # Connect to postgres database to create new database
        default_db = await DatabaseRegistry.get_database("postgres")

        try:
            # Check if database already exists
            cls._logger.debug(f"Checking if database {db_name} already exists")
            query = "SELECT 1 FROM pg_database WHERE datname = $1"
            exists = await default_db.fetchval(query, db_name)

            if exists:
                cls._logger.warning(f"Database {db_name} already exists")
                return False

            # Create database (cannot use parameters for database name)
            cls._logger.debug(f"Creating database: {db_name}")
            query = f'CREATE DATABASE "{db_name}"'
            await default_db.execute(query)

            creation_duration = time.perf_counter() - start_time
            cls._logger.debug(f"Database {db_name} created in {creation_duration:.3f}s")

            # Initialize the database with base addon and tables
            cls._logger.debug(f"Initializing database {db_name}")
            init_success = await DatabaseInitializer.initialize_database(db_name)

            if not init_success:
                cls._logger.error(
                    f"Database initialization failed for {db_name}, rolling back..."
                )
                # Rollback database creation if initialization fails
                await cls.rollback_database_creation(db_name)
                return False

            total_duration = time.perf_counter() - start_time
            cls._logger.info(
                f"Database {db_name} created and initialized in {total_duration:.3f}s"
            )
            return True

        except Exception as e:
            duration = time.perf_counter() - start_time
            cls._logger.error(
                f"Error creating database {db_name} after {duration:.3f}s: {e}"
            )
            # Attempt rollback if database was created but initialization failed
            try:
                await cls.rollback_database_creation(db_name)
            except Exception as rollback_error:
                cls._logger.error(
                    f"Error during rollback of {db_name}: {rollback_error}"
                )
            return False

    @classmethod
    async def drop_database(cls, db_name: str) -> bool:
        """Drop a database"""
        if db_name in ["postgres", "template0", "template1"]:
            print(f"Cannot drop system database: {db_name}")
            return False

        # Close connection to the database if it exists
        if db_name in DatabaseRegistry._databases:
            await DatabaseRegistry._databases[db_name].close_pool()
            del DatabaseRegistry._databases[db_name]

        # Connect to postgres database to drop the target database
        default_db = await DatabaseRegistry.get_database("postgres")

        try:
            # Terminate connections to the target database
            terminate_query = """
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = $1 AND pid <> pg_backend_pid()
            """
            await default_db.execute(terminate_query, db_name)

            # Drop database (cannot use parameters for database name)
            query = f'DROP DATABASE IF EXISTS "{db_name}"'
            await default_db.execute(query)
            print(f"Database {db_name} dropped successfully")
            return True

        except Exception as e:
            print(f"Error dropping database {db_name}: {e}")
            return False

    @classmethod
    async def rollback_database_creation(cls, db_name: str) -> bool:
        """
        Rollback database creation by dropping the database
        Used when base addon installation fails

        Args:
            db_name: Name of the database to drop

        Returns:
            True if successful, False otherwise
        """
        try:
            # Close connection to the database being dropped
            if db_name in DatabaseRegistry._databases:
                await DatabaseRegistry._databases[db_name].close_pool()
                del DatabaseRegistry._databases[db_name]

            # Reset current database if it's the one being dropped
            if DatabaseRegistry._current_db == db_name:
                DatabaseRegistry._current_db = None

            # Connect to postgres database to drop the target database
            default_db = await DatabaseRegistry.get_database("postgres")

            # Terminate all connections to the target database
            await default_db.execute(
                """SELECT pg_terminate_backend(pid)
                   FROM pg_stat_activity
                   WHERE datname = $1 AND pid <> pg_backend_pid()""",
                db_name,
            )

            # Drop the database
            query = f'DROP DATABASE IF EXISTS "{db_name}"'
            await default_db.execute(query)

            print(f"Database {db_name} rolled back (dropped) successfully")
            return True

        except Exception as e:
            print(f"Error rolling back database {db_name}: {e}")
            return False
