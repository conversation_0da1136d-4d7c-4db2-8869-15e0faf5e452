"""
Error response creation for FastAPI routes
Handles both JSON RPC and HTML error responses
"""

from typing import Op<PERSON>, Union

from fastapi import Request
from fastapi.responses import HTMLResponse, JSONResponse

from ..interfaces import RouteInfo
from ..metadata import RouteType
from .html_errors import HTMLErrorResponse
from .jsonrpc import JsonRpcError
from .route_detection import RouteTypeDetector


class ErrorResponseFactory:
    """Factory for creating appropriate error responses based on route type"""

    @staticmethod
    def create_error_response(
        error: Exception,
        request: Request,
        route_info: Optional[RouteInfo] = None,
        status_code: int = 500,
        request_id: Optional[Union[str, int]] = None,
    ) -> Union[JSONResponse, HTMLResponse]:
        """
        Create appropriate error response based on route type

        Args:
            error: The exception that occurred
            request: FastAPI request object
            route_info: Optional route information
            status_code: HTTP status code
            request_id: JSON RPC request ID (if applicable)

        Returns:
            JSONResponse for JSON RPC routes, HTMLResponse for HTTP routes
        """
        route_type = RouteTypeDetector.detect_route_type(request, route_info)

        if route_type == RouteType.JSON:
            return ErrorResponseFactory._create_jsonrpc_error_response(
                error, request_id
            )
        else:
            return ErrorResponseFactory._create_html_error_response(
                error, request, status_code
            )

    @staticmethod
    def _create_jsonrpc_error_response(
        error: Exception, request_id: Optional[Union[str, int]] = None
    ) -> JSONResponse:
        """Create JSON RPC error response"""
        return JsonRpcError.from_exception(
            error, request_id=request_id, include_stacktrace=True
        ).to_response()

    @staticmethod
    def _create_html_error_response(
        error: Exception, request: Request, status_code: int = 500
    ) -> HTMLResponse:
        """Create HTML error response"""
        # Gather request information
        request_info = {
            "Method": request.method,
            "URL": str(request.url),
            "User-Agent": request.headers.get("user-agent", "Unknown"),
            "Content-Type": request.headers.get("content-type", "Not specified"),
            "Client IP": (
                getattr(request.client, "host", "Unknown")
                if request.client
                else "Unknown"
            ),
        }

        return HTMLErrorResponse.create_error_page(
            error=error,
            status_code=status_code,
            request_info=request_info,
            include_stacktrace=True,
        )

    @staticmethod
    def create_simple_error_response(
        message: str,
        request: Request,
        route_info: Optional[RouteInfo] = None,
        status_code: int = 500,
        request_id: Optional[Union[str, int]] = None,
    ) -> Union[JSONResponse, HTMLResponse]:
        """
        Create simple error response without exception details

        Args:
            message: Error message
            request: FastAPI request object
            route_info: Optional route information
            status_code: HTTP status code
            request_id: JSON RPC request ID (if applicable)

        Returns:
            JSONResponse for JSON RPC routes, HTMLResponse for HTTP routes
        """
        route_type = RouteTypeDetector.detect_route_type(request, route_info)

        if route_type == RouteType.JSON:
            return JsonRpcError.internal_error(
                message=message, request_id=request_id, include_stacktrace=False
            ).to_response()
        else:
            return HTMLErrorResponse.create_simple_error_page(
                message=message, status_code=status_code
            )


def create_appropriate_error_response(
    error: Exception,
    request: Request,
    route_info: Optional[RouteInfo] = None,
    status_code: int = 500,
    request_id: Optional[Union[str, int]] = None,
) -> Union[JSONResponse, HTMLResponse]:
    """
    Create appropriate error response based on route type.

    This is a convenience function that delegates to ErrorResponseFactory.
    """
    return ErrorResponseFactory.create_error_response(
        error=error,
        request=request,
        route_info=route_info,
        status_code=status_code,
        request_id=request_id,
    )
