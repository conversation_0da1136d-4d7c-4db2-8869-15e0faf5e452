"""
SQL Helper Utilities for XML Data Loading

This module provides reusable SQL helper utilities for XML data loading
that are completely independent of AppRegistry and model registry.
All operations use raw SQL queries for maximum independence.
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from ..database.connection.manager import DatabaseManager
from ..logging import get_logger


class SQLHelpers:
    """
    Reusable SQL helper utilities for XML data loading.

    This class provides direct database access without any dependency on
    AppRegistry or model registry, using only raw SQL queries.
    """

    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize SQL helpers with database manager.

        Args:
            db_manager: Database manager instance for executing queries
        """
        self.db_manager = db_manager
        self.logger = get_logger(__name__)

    async def validate_model_exists(self, model_name: str) -> bool:
        """
        Validate that a model exists in ir_model table using raw SQL.

        Args:
            model_name: Model name to validate

        Returns:
            True if model exists, False otherwise
        """
        try:
            query = "SELECT COUNT(*) FROM ir_model WHERE model = $1"
            count = await self.db_manager.fetchval(query, model_name)
            return count > 0
        except Exception as e:
            self.logger.error(f"Error validating model {model_name}: {e}")
            return False

    async def validate_xmlid_exists(self, xml_id: str) -> bool:
        """
        Validate that an XML ID exists in ir_model_data table using raw SQL.

        Args:
            xml_id: XML ID in format 'module.name' or just 'name'

        Returns:
            True if XML ID exists, False otherwise
        """
        try:
            if "." in xml_id:
                module, name = xml_id.split(".", 1)
                query = (
                    "SELECT COUNT(*) FROM ir_model_data WHERE module = $1 AND name = $2"
                )
                count = await self.db_manager.fetchval(query, module, name)
            else:
                query = "SELECT COUNT(*) FROM ir_model_data WHERE name = $1"
                count = await self.db_manager.fetchval(query, xml_id)
            return count > 0
        except Exception as e:
            self.logger.error(f"Error validating XML ID {xml_id}: {e}")
            return False

    async def validate_field_exists(self, model_name: str, field_name: str) -> bool:
        """
        Validate that a field exists for a model using raw SQL.

        Args:
            model_name: Model name
            field_name: Field name to validate

        Returns:
            True if field exists, False otherwise
        """
        try:
            query = """
                SELECT COUNT(*) FROM ir_model_fields
                WHERE model = $1 AND name = $2
            """
            count = await self.db_manager.fetchval(query, model_name, field_name)
            return count > 0
        except Exception as e:
            self.logger.error(
                f"Error validating field {field_name} for model {model_name}: {e}"
            )
            return False

    async def get_field_type(self, model_name: str, field_name: str) -> Optional[str]:
        """
        Get field type for a model field using raw SQL.

        Args:
            model_name: Model name
            field_name: Field name

        Returns:
            Field type string or None if not found
        """
        try:
            query = """
                SELECT ttype FROM ir_model_fields
                WHERE model = $1 AND name = $2
            """
            return await self.db_manager.fetchval(query, model_name, field_name)
        except Exception as e:
            self.logger.error(
                f"Error getting field type for {field_name} in {model_name}: {e}"
            )
            return None

    async def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists in the database.

        Args:
            table_name: Name of the table to check

        Returns:
            True if table exists, False otherwise
        """
        query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = $1
            )
        """
        try:
            result = await self.db_manager.fetchval(query, table_name)
            return bool(result)
        except Exception as e:
            self.logger.error(f"Error checking if table {table_name} exists: {e}")
            return False

    async def get_table_columns(self, table_name: str) -> List[str]:
        """
        Get list of column names for a table.

        Args:
            table_name: Name of the table

        Returns:
            List of column names
        """
        query = """
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = $1
            ORDER BY ordinal_position
        """
        try:
            rows = await self.db_manager.fetch(query, table_name)
            return [row["column_name"] for row in rows]
        except Exception as e:
            self.logger.error(f"Error getting columns for table {table_name}: {e}")
            return []

    async def insert_record(
        self, table_name: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """
        Insert a record into a table and return the ID.

        Args:
            table_name: Name of the table
            data: Dictionary of column names to values

        Returns:
            The ID of the inserted record, or None if failed
        """
        if not data:
            return None

        # Ensure we have an ID
        if "id" not in data:
            data["id"] = str(uuid.uuid4())

        # Add timestamps if columns exist
        columns = await self.get_table_columns(table_name)
        if "create_date" in columns and "create_date" not in data:
            data["create_date"] = datetime.now(timezone.utc)
        if "write_date" in columns and "write_date" not in data:
            data["write_date"] = datetime.now(timezone.utc)

        # Build the INSERT query
        column_names = list(data.keys())
        placeholders = [f"${i+1}" for i in range(len(column_names))]
        values = [data[col] for col in column_names]

        query = f"""
            INSERT INTO {table_name} ({', '.join(column_names)})
            VALUES ({', '.join(placeholders)})
            RETURNING id
        """

        try:
            result = await self.db_manager.fetchval(query, *values)
            self.logger.debug(f"Inserted record into {table_name} with ID: {result}")
            return str(result)
        except Exception as e:
            self.logger.error(f"Error inserting record into {table_name}: {e}")
            return None

    async def update_record(
        self, table_name: str, record_id: str, data: Dict[str, Any]
    ) -> bool:
        """
        Update a record in a table.

        Args:
            table_name: Name of the table
            record_id: ID of the record to update
            data: Dictionary of column names to values

        Returns:
            True if update was successful, False otherwise
        """
        if not data:
            return True

        # Add write timestamp if column exists
        columns = await self.get_table_columns(table_name)
        if "write_date" in columns and "write_date" not in data:
            data["write_date"] = datetime.now(timezone.utc)

        # Build the UPDATE query
        set_clauses = [f"{col} = ${i+2}" for i, col in enumerate(data.keys())]
        values = [record_id] + list(data.values())

        query = f"""
            UPDATE {table_name}
            SET {', '.join(set_clauses)}
            WHERE id = $1
        """

        try:
            await self.db_manager.execute(query, *values)
            self.logger.debug("Updated record %s in %s", record_id, table_name)
            return True
        except Exception as e:
            self.logger.error(f"Error updating record {record_id} in {table_name}: {e}")
            return False

    async def find_record(
        self, table_name: str, conditions: Dict[str, Any], limit: int = 1
    ) -> Optional[Dict[str, Any]]:
        """
        Find a record in a table based on conditions.

        Args:
            table_name: Name of the table
            conditions: Dictionary of column names to values for WHERE clause
            limit: Maximum number of records to return

        Returns:
            Dictionary representing the record, or None if not found
        """
        if not conditions:
            return None

        # Build the WHERE clause
        where_clauses = [f"{col} = ${i+1}" for i, col in enumerate(conditions.keys())]
        values = list(conditions.values())

        query = f"""
            SELECT * FROM {table_name}
            WHERE {' AND '.join(where_clauses)}
            LIMIT {limit}
        """

        try:
            if limit == 1:
                row = await self.db_manager.fetchrow(query, *values)
                return dict(row) if row else None
            else:
                rows = await self.db_manager.fetch(query, *values)
                return [dict(row) for row in rows]
        except Exception as e:
            self.logger.error(f"Error finding record in {table_name}: {e}")
            return None

    async def find_records(
        self, table_name: str, conditions: Dict[str, Any], limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Find multiple records in a table based on conditions.

        Args:
            table_name: Name of the table
            conditions: Dictionary of column names to values for WHERE clause
            limit: Maximum number of records to return (None for no limit)

        Returns:
            List of dictionaries representing the records
        """
        if not conditions:
            return []

        # Build the WHERE clause
        where_clauses = [f"{col} = ${i+1}" for i, col in enumerate(conditions.keys())]
        values = list(conditions.values())

        query = f"""
            SELECT * FROM {table_name}
            WHERE {' AND '.join(where_clauses)}
        """

        if limit:
            query += f" LIMIT {limit}"

        try:
            rows = await self.db_manager.fetch(query, *values)
            return [dict(row) for row in rows]
        except Exception as e:
            self.logger.error(f"Error finding records in {table_name}: {e}")
            return []

    async def record_exists(self, table_name: str, conditions: Dict[str, Any]) -> bool:
        """
        Check if a record exists in a table.

        Args:
            table_name: Name of the table
            conditions: Dictionary of column names to values for WHERE clause

        Returns:
            True if record exists, False otherwise
        """
        if not conditions:
            return False

        # Build the WHERE clause
        where_clauses = [f"{col} = ${i+1}" for i, col in enumerate(conditions.keys())]
        values = list(conditions.values())

        query = f"""
            SELECT EXISTS (
                SELECT 1 FROM {table_name}
                WHERE {' AND '.join(where_clauses)}
            )
        """

        try:
            result = await self.db_manager.fetchval(query, *values)
            return bool(result)
        except Exception as e:
            self.logger.error(f"Error checking if record exists in {table_name}: {e}")
            return False


class XMLIDSQLHelpers:
    """
    SQL helper utilities specifically for XML ID management.

    This class provides direct SQL access to ir_model_data table
    without any dependency on AppRegistry or model registry.
    """

    def __init__(self, sql_helpers: SQLHelpers):
        """
        Initialize XML ID SQL helpers.

        Args:
            sql_helpers: Base SQL helpers instance
        """
        self.sql = sql_helpers
        self.logger = get_logger(__name__)
        self.table_name = "ir_model_data"

    async def ensure_table_exists(self) -> bool:
        """
        Ensure the ir_model_data table exists.

        Returns:
            True if table exists or was created, False otherwise
        """
        if await self.sql.table_exists(self.table_name):
            return True

        # Create the table if it doesn't exist
        create_query = f"""
            CREATE TABLE IF NOT EXISTS {self.table_name} (
                id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid()::text,
                module VARCHAR(255) NOT NULL,
                name VARCHAR(255) NOT NULL,
                model VARCHAR(255) NOT NULL,
                res_id VARCHAR(255) NOT NULL,
                noupdate BOOLEAN DEFAULT FALSE,
                create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                write_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(module, name)
            )
        """

        try:
            await self.sql.db_manager.execute(create_query)
            self.logger.info(f"Created {self.table_name} table")
            return True
        except Exception as e:
            self.logger.error(f"Error creating {self.table_name} table: {e}")
            return False

    async def xmlid_lookup(self, xml_id: str) -> Optional[Dict[str, Any]]:
        """
        Look up a record by its XML ID using raw SQL.

        Args:
            xml_id: XML ID in format 'module.name' or just 'name'

        Returns:
            Dictionary with model and res_id, or None if not found
        """
        await self.ensure_table_exists()

        if "." in xml_id:
            module, name = xml_id.split(".", 1)
            conditions = {"module": module, "name": name}
        else:
            # If no module specified, search by name only
            conditions = {"name": xml_id}

        record = await self.sql.find_record(self.table_name, conditions)
        if record:
            return {
                "model": record["model"],
                "res_id": record["res_id"],
                "id": record["res_id"],  # For compatibility
            }
        return None

    async def create_or_update_xmlid(
        self, module: str, name: str, model: str, res_id: str, noupdate: bool = False
    ) -> bool:
        """
        Create or update an XML ID mapping using raw SQL.

        Args:
            module: Module name
            name: XML ID name
            model: Model name
            res_id: Record ID
            noupdate: No update flag

        Returns:
            True if successful, False otherwise
        """
        await self.ensure_table_exists()

        # Check if XML ID already exists
        existing = await self.sql.find_record(
            self.table_name, {"module": module, "name": name}
        )

        if existing:
            # Update existing record if noupdate is False
            if not existing.get("noupdate", False):
                update_data = {"model": model, "res_id": res_id, "noupdate": noupdate}
                success = await self.sql.update_record(
                    self.table_name, existing["id"], update_data
                )
                if success:
                    self.logger.debug(
                        f"Updated XML ID mapping: {module}.{name} -> {model}({res_id})"
                    )
                return success
            else:
                self.logger.debug(
                    f"Skipped updating XML ID {module}.{name} (noupdate=True)"
                )
                return True
        else:
            # Create new record
            data = {
                "module": module,
                "name": name,
                "model": model,
                "res_id": res_id,
                "noupdate": noupdate,
            }
            record_id = await self.sql.insert_record(self.table_name, data)
            if record_id:
                self.logger.debug(
                    f"Created XML ID mapping: {module}.{name} -> {model}({res_id})"
                )
                return True
            return False

    async def xmlid_to_res_id(self, xml_id: str) -> Optional[str]:
        """
        Convert XML ID to record ID using raw SQL.

        Args:
            xml_id: XML ID in format 'module.name' or just 'name'

        Returns:
            Record ID as string, or None if not found
        """
        result = await self.xmlid_lookup(xml_id)
        return result["res_id"] if result else None

    async def get_xmlid_by_record(self, model: str, res_id: str) -> Optional[str]:
        """
        Get XML ID for a record using raw SQL.

        Args:
            model: Model name
            res_id: Record ID

        Returns:
            XML ID in format 'module.name', or None if not found
        """
        await self.ensure_table_exists()

        record = await self.sql.find_record(
            self.table_name, {"model": model, "res_id": res_id}
        )
        if record:
            return f"{record['module']}.{record['name']}"
        return None

    async def delete_xmlid(self, xml_id: str) -> bool:
        """
        Delete an XML ID mapping using raw SQL.

        Args:
            xml_id: XML ID in format 'module.name' or just 'name'

        Returns:
            True if successful, False otherwise
        """
        await self.ensure_table_exists()

        if "." in xml_id:
            module, name = xml_id.split(".", 1)
            conditions = {"module": module, "name": name}
        else:
            conditions = {"name": xml_id}

        # Find the record first
        record = await self.sql.find_record(self.table_name, conditions)
        if not record:
            return False

        # Delete the record
        query = f"DELETE FROM {self.table_name} WHERE id = $1"
        try:
            await self.sql.db_manager.execute(query, record["id"])
            self.logger.debug(f"Deleted XML ID mapping: {xml_id}")
            return True
        except Exception as e:
            self.logger.error(f"Error deleting XML ID {xml_id}: {e}")
            return False

    async def validate_xmlid_reference(
        self, xml_id: str, expected_model: str = None
    ) -> Dict[str, Any]:
        """
        Validate an XML ID reference and optionally check the model type.

        Args:
            xml_id: XML ID in format 'module.name' or just 'name'
            expected_model: Expected model name (optional)

        Returns:
            Dictionary with validation result and details
        """
        result = {
            "valid": False,
            "exists": False,
            "model": None,
            "res_id": None,
            "error": None,
        }

        try:
            # Check if XML ID exists
            xmlid_data = await self.xmlid_lookup(xml_id)
            if not xmlid_data:
                result["error"] = f"XML ID '{xml_id}' not found"
                return result

            result["exists"] = True
            result["model"] = xmlid_data["model"]
            result["res_id"] = xmlid_data["res_id"]

            # Check model if specified
            if expected_model and xmlid_data["model"] != expected_model:
                result["error"] = (
                    f"XML ID '{xml_id}' points to model '{xmlid_data['model']}', expected '{expected_model}'"
                )
                return result

            # Validate that the referenced record actually exists
            table_name = xmlid_data["model"].replace(".", "_")
            if await self.sql.table_exists(table_name):
                record_exists = await self.sql.find_record(
                    table_name, {"id": xmlid_data["res_id"]}
                )
                if not record_exists:
                    result["error"] = (
                        f"Referenced record {xmlid_data['res_id']} does not exist in {xmlid_data['model']}"
                    )
                    return result

            result["valid"] = True
            return result

        except Exception as e:
            result["error"] = f"Error validating XML ID reference: {e}"
            self.logger.error(f"Error validating XML ID reference {xml_id}: {e}")
            return result

    async def validate_xmlid_batch(
        self, xml_ids: List[str]
    ) -> Dict[str, Dict[str, Any]]:
        """
        Validate multiple XML IDs in a single batch operation.

        Args:
            xml_ids: List of XML IDs to validate

        Returns:
            Dictionary mapping XML ID to validation result
        """
        results = {}

        if not xml_ids:
            return results

        try:
            # Build batch query for efficiency
            conditions = []
            values = []

            for xml_id in xml_ids:
                if "." in xml_id:
                    module, name = xml_id.split(".", 1)
                    conditions.append(
                        f"(module = ${len(values)+1} AND name = ${len(values)+2})"
                    )
                    values.extend([module, name])
                else:
                    conditions.append(f"name = ${len(values)+1}")
                    values.append(xml_id)

            if conditions:
                query = f"""
                    SELECT module, name, model, res_id
                    FROM {self.table_name}
                    WHERE {' OR '.join(conditions)}
                """

                records = await self.sql.db_manager.fetch(query, *values)

                # Create lookup map
                found_xmlids = {}
                for record in records:
                    full_xmlid = f"{record['module']}.{record['name']}"
                    found_xmlids[full_xmlid] = {
                        "model": record["model"],
                        "res_id": record["res_id"],
                    }
                    # Also add by name only for lookup
                    found_xmlids[record["name"]] = {
                        "model": record["model"],
                        "res_id": record["res_id"],
                    }

                # Process results
                for xml_id in xml_ids:
                    if xml_id in found_xmlids:
                        results[xml_id] = {
                            "valid": True,
                            "exists": True,
                            "model": found_xmlids[xml_id]["model"],
                            "res_id": found_xmlids[xml_id]["res_id"],
                            "error": None,
                        }
                    else:
                        results[xml_id] = {
                            "valid": False,
                            "exists": False,
                            "model": None,
                            "res_id": None,
                            "error": f"XML ID '{xml_id}' not found",
                        }

        except Exception as e:
            self.logger.error(f"Error in batch XML ID validation: {e}")
            # Return error results for all XML IDs
            for xml_id in xml_ids:
                results[xml_id] = {
                    "valid": False,
                    "exists": False,
                    "model": None,
                    "res_id": None,
                    "error": f"Batch validation error: {e}",
                }

        return results


class ModelSQLHelpers:
    """
    SQL helper utilities for model operations without AppRegistry dependency.

    This class provides direct SQL access to model tables
    without any dependency on AppRegistry or model registry.
    """

    def __init__(self, sql_helpers: SQLHelpers):
        """
        Initialize model SQL helpers.

        Args:
            sql_helpers: Base SQL helpers instance
        """
        self.sql = sql_helpers
        self.logger = get_logger(__name__)

    def _model_to_table(self, model_name: str) -> str:
        """
        Convert model name to table name.

        Args:
            model_name: Model name (e.g., 'ir.model.data')

        Returns:
            Table name (e.g., 'ir_model_data')
        """
        return model_name.replace(".", "_")

    async def create_record(
        self, model_name: str, values: Dict[str, Any]
    ) -> Optional[str]:
        """
        Create a record in a model table using raw SQL.

        Args:
            model_name: Model name
            values: Dictionary of field names to values

        Returns:
            The ID of the created record, or None if failed
        """
        table_name = self._model_to_table(model_name)

        # Check if table exists
        if not await self.sql.table_exists(table_name):
            self.logger.error(
                f"Table {table_name} for model {model_name} does not exist"
            )
            return None

        return await self.sql.insert_record(table_name, values)

    async def update_record(
        self, model_name: str, record_id: str, values: Dict[str, Any]
    ) -> bool:
        """
        Update a record in a model table using raw SQL.

        Args:
            model_name: Model name
            record_id: ID of the record to update
            values: Dictionary of field names to values

        Returns:
            True if successful, False otherwise
        """
        table_name = self._model_to_table(model_name)

        # Check if table exists
        if not await self.sql.table_exists(table_name):
            self.logger.error(
                f"Table {table_name} for model {model_name} does not exist"
            )
            return False

        return await self.sql.update_record(table_name, record_id, values)

    async def find_record_by_id(
        self, model_name: str, record_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Find a record by ID in a model table using raw SQL.

        Args:
            model_name: Model name
            record_id: ID of the record

        Returns:
            Dictionary representing the record, or None if not found
        """
        table_name = self._model_to_table(model_name)

        # Check if table exists
        if not await self.sql.table_exists(table_name):
            self.logger.error(
                f"Table {table_name} for model {model_name} does not exist"
            )
            return None

        return await self.sql.find_record(table_name, {"id": record_id})

    async def search_records(
        self, model_name: str, domain: Dict[str, Any], limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for records in a model table using raw SQL.

        Args:
            model_name: Model name
            domain: Dictionary of field names to values for search
            limit: Maximum number of records to return

        Returns:
            List of dictionaries representing the records
        """
        table_name = self._model_to_table(model_name)

        # Check if table exists
        if not await self.sql.table_exists(table_name):
            self.logger.error(
                f"Table {table_name} for model {model_name} does not exist"
            )
            return []

        return await self.sql.find_records(table_name, domain, limit)

    async def validate_model_table_exists(self, model_name: str) -> bool:
        """
        Validate that the table for a model exists using raw SQL.

        Args:
            model_name: Model name to validate

        Returns:
            True if table exists, False otherwise
        """
        table_name = self._model_to_table(model_name)
        return await self.sql.table_exists(table_name)

    async def validate_record_exists(self, model_name: str, record_id: str) -> bool:
        """
        Validate that a specific record exists in a model table.

        Args:
            model_name: Model name
            record_id: Record ID to check

        Returns:
            True if record exists, False otherwise
        """
        table_name = self._model_to_table(model_name)

        if not await self.sql.table_exists(table_name):
            return False

        try:
            record = await self.sql.find_record(table_name, {"id": record_id})
            return record is not None
        except Exception as e:
            self.logger.error("Error checking record existence: %s", e)
            return False

    async def get_model_fields(self, model_name: str) -> List[Dict[str, Any]]:
        """
        Get field information for a model using raw SQL.

        Args:
            model_name: Model name

        Returns:
            List of field information dictionaries
        """
        try:
            query = """
                SELECT name, ttype, required, readonly, relation
                FROM ir_model_fields
                WHERE model = $1
                ORDER BY name
            """
            records = await self.sql.db_manager.fetch(query, model_name)
            return [dict(record) for record in records]
        except Exception as e:
            self.logger.error("Error getting model fields for %s: %s", model_name, e)
            return []
