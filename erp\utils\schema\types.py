"""
Schema type definitions and utility functions
Contains PostgreSQL type mappings and helper functions
"""

import re
from typing import Any, Dict, Optional


def camel_to_snake_case(name: str) -> str:
    """Convert camelCase to snake_case for database column names"""
    # Insert an underscore before any uppercase letter that follows a lowercase letter or digit
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    # Insert an underscore before any uppercase letter that follows a lowercase letter
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()


def get_field_type_mapping() -> Dict[str, str]:
    """Get mapping of ERP field types to PostgreSQL types"""
    return {
        "Char": "VARCHAR",
        "Text": "TEXT",
        "Boolean": "BOOLEAN",
        "Selection": "VARCHAR",
        "Integer": "INTEGER",
        "Float": "REAL",
        "Date": "DATE",
        "Datetime": "TIMESTAMP",
        "Many2One": "UUID",
        "One2One": "UUID",
        "Reference": "VARCHAR",
        "Binary": "BYTEA",
        "Json": "JSONB",
        "Html": "TEXT",
    }


def get_postgresql_type_to_erp_mapping() -> Dict[str, str]:
    """Convert PostgreSQL type to ERP field type"""
    return {
        "VARCHAR": "char",
        "TEXT": "text",
        "BOOLEAN": "boolean",
        "INTEGER": "integer",
        "REAL": "float",
        "DATE": "date",
        "TIMESTAMP": "datetime",
        "UUID": "char",  # UUIDs are stored as char fields in ERP
        "BYTEA": "binary",
        "JSONB": "text",  # JSON stored as text in ERP
    }


def map_field_type_to_postgresql(field_type: str, field_obj=None) -> str:
    """
    Map ERP field type to PostgreSQL type with size considerations

    Args:
        field_type: ERP field type name
        field_obj: Optional field object for size information

    Returns:
        PostgreSQL type string
    """
    type_mapping = get_field_type_mapping()
    pg_type = type_mapping.get(field_type, "TEXT")

    # Handle size for VARCHAR fields
    if pg_type == "VARCHAR" and field_obj:
        if hasattr(field_obj, "size") and field_obj.size:
            pg_type = f"VARCHAR({field_obj.size})"
        elif field_type == "Char":
            # Default size for Char fields
            pg_type = "VARCHAR(255)"
        elif field_type == "Reference":
            # Reference fields need enough space for "model,id"
            pg_type = "VARCHAR(255)"
    elif pg_type == "VARCHAR" and field_type == "Char":
        # Default size for Char fields when no field object
        pg_type = "VARCHAR(255)"
    elif pg_type == "VARCHAR" and field_type == "Reference":
        # Reference fields need enough space for "model,id"
        pg_type = "VARCHAR(255)"

    return pg_type


def pg_type_to_erp_type(pg_type: str) -> str:
    """Convert PostgreSQL type to ERP field type"""
    pg_type_upper = pg_type.upper()
    mapping = get_postgresql_type_to_erp_mapping()

    # Handle VARCHAR with size
    if pg_type_upper.startswith("VARCHAR"):
        return "char"

    return mapping.get(pg_type_upper, "char")  # Default fallback


def get_default_value_sql(field_info: Dict[str, Any]) -> Optional[str]:
    """
    Generate SQL default value from field information

    Args:
        field_info: Dictionary containing field information

    Returns:
        SQL default value string or None
    """
    if field_info.get("default") is None:
        return None

    default_val = field_info["default"]

    # Handle special SQL functions
    if isinstance(default_val, str) and default_val.startswith(
        ("CURRENT_TIMESTAMP", "gen_random_uuid()", "TRUE", "FALSE")
    ):
        return default_val

    # Handle string values
    if isinstance(default_val, str) and not default_val.startswith("'"):
        return f"'{default_val}'"

    # Handle boolean values
    if isinstance(default_val, bool):
        return str(default_val).upper()

    # Return as-is for other types
    return str(default_val)


def validate_field_info(field_info: Dict[str, Any]) -> bool:
    """
    Validate field information dictionary

    Args:
        field_info: Field information dictionary

    Returns:
        True if valid, False otherwise
    """
    required_keys = ["name", "type"]

    for key in required_keys:
        if key not in field_info:
            return False

    # Validate field type
    valid_types = get_field_type_mapping().keys()
    if field_info["type"] not in valid_types and not field_info["type"].startswith(
        ("VARCHAR", "UUID")
    ):
        return False

    return True
